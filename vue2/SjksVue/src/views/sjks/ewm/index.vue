<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" size="mini" icon="el-icon-upload2"
                       v-if="this.$store.getters.permissions.indexOf('JDWSJ02EWM01QX02') > -1"
                       @click="">
              生成
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
          </div>
        </div>
        <div class="table-box">
          <Table :tableData="list" :tableOptions="realTableOptions" :loading="loading"
                 @getCurrentData="select">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="userId" slot-scope="scope">
              <span v-if="scope.row.userId">是</span>
              <span v-else>否</span>
            </template>
          </Table>
          <Pagination @handleRefresh="handleCurrentChange" :queryParam="queryParams"
                      layout="total, sizes, prev, pager, next, jumper" :total="queryParams.total" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import {getList} from "api/sjks/qrcode";

export default {
  name: 'index',
  components: { Table, Pagination, Dropdown },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      realTableOptions: [],
      tableOptions: [
        { label: '二维码编号', prop: 'qrCode' },
        { label: '是否绑定', prop: 'userId', slot: true },
        { label: '绑定时间', prop: 'bindingTime' },
      ],
      loading: false,
      list: [],
    };
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },


    // 查询方法
    query() {
      this.loading = true
      getList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },


  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
