import request from '@/utils/request'

/**
 * 二维码列表
 * @param params
 * @returns {*}
 */
export function getList (params) {
    return request({
        url: '/dwSjks/sjks/ewm',
        method: 'post',
        data: params
    })
}

/**
 * 生成二维码
 * @param params
 * @returns {*}
 */
export function generateQrcode (params) {
    return request({
        url: '/dwSjks/sjks/ewm/generate',
        method: 'post',
        data: params
    })
}

/**
 * 导出二维码PDF
 * @param qrCodeId
 * @returns {*}
 */
export function exportQrcodePdf (qrCodeId) {
    return request({
        url: `/dwSjks/sjks/ewm/exportPdf/${qrCodeId}`,
        method: 'get',
        responseType: 'blob'
    })
}