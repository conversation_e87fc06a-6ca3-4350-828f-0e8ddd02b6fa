package com.soft.gcc.xtbg.sjks.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.SjksBaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设计-考试-二维码子表
 * <AUTHOR>
 * @TableName sj_ks_qrcode_detail
 */
@TableName(value ="sj_ks_qrcode_detail")
@Data
public class SjKsQrcodeDetail extends SjksBaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 二维码主表id
     */
    @TableField(value = "qr_code_id")
    private Integer qrCodeId;

    /**
     * 二维码
     */
    @TableField(value = "qr_code")
    private String qrCode;

    /**
     * 绑定用户ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 绑定用户时间
     */
    @TableField(value = "binding_time")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date bindingTime;

    /**
     * 录入成绩
     */
    @TableField(value = "input_score")
    private BigDecimal inputScore;

    /**
     * 录入人
     */
    @TableField(value = "input_user_id")
    private Integer inputUserId;

    /**
     * 录入时间
     */
    @TableField(value = "input_time")
    private Date inputTime;
}