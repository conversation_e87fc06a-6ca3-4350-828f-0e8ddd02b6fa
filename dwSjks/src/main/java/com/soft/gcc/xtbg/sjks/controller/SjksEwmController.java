package com.soft.gcc.xtbg.sjks.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.params.GenerateQrcodeParams;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@RequestMapping("/sjks/ewm")
@RestController
public class SjksEwmController extends BaseController {

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeService sjKsQrcodeService;

    @PostMapping("/getList")
    public Result<Object> getList(@RequestBody SjKsQrcode sjKsQrcode) {
        return Result.ok(sjKsQrcodeDetailService.getList(sjKsQrcode));
    }

    @PostMapping("/generate")
    public ResponseEntity<?> generateQrcode(@Valid @RequestBody GenerateQrcodeParams params) {
        System.out.println("开始生成二维码，数量：" + params.getQuantity());
        try {
            PersonEntity persen = user();
            System.out.println("获取用户信息：" + (persen != null ? persen.getId() : "null"));

            if (persen == null) {
                System.out.println("用户未登录");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(Result.error("用户未登录"));
            }

            System.out.println("开始调用service生成PDF");
            byte[] pdfBytes = sjKsQrcodeService.generateQrcodeAndExportPdf(params.getQuantity(), persen);
            System.out.println("PDF生成成功，大小：" + pdfBytes.length + " bytes");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "二维码_" + System.currentTimeMillis() + ".pdf");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);
        } catch (Exception e) {
            System.out.println("生成失败，异常信息：" + e.getMessage());
            e.printStackTrace(); // 打印异常信息用于调试
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Result.error("生成失败：" + e.getMessage()));
        }
    }

}
