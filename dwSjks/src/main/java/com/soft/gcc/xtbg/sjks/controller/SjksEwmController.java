package com.soft.gcc.xtbg.sjks.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.params.GenerateQrcodeParams;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@RequestMapping("/sjks/ewm")
@RestController
public class SjksEwmController extends BaseController {

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeService sjKsQrcodeService;

    @PostMapping("/getList")
    public Result<Object> getList(@RequestBody SjKsQrcode sjKsQrcode) {
        return Result.ok(sjKsQrcodeDetailService.getList(sjKsQrcode));
    }

    @PostMapping("/generate")
    public Result<Object> generateQrcode(@Valid @RequestBody GenerateQrcodeParams params) {
        try {
            sjKsQrcodeService.generateQrcode(params.getQuantity());
            return Result.ok("生成成功");
        } catch (Exception e) {
            return Result.error("生成失败：" + e.getMessage());
        }
    }

}
