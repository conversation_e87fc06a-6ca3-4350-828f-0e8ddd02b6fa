package com.soft.gcc.xtbg.sjks.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.params.GenerateQrcodeParams;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@RequestMapping("/sjks/ewm")
@RestController
public class SjksEwmController extends BaseController {

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeService sjKsQrcodeService;

    @PostMapping("/getList")
    public Result<Object> getList(@RequestBody SjKsQrcode sjKsQrcode) {
        return Result.ok(sjKsQrcodeDetailService.getList(sjKsQrcode));
    }

    @PostMapping("/generate")
    public ResponseEntity<byte[]> generateQrcode(@Valid @RequestBody GenerateQrcodeParams params) {
        try {
            PersonEntity persen = user();
            if (persen == null) {
                throw new RuntimeException("用户未登录");
            }
            byte[] pdfBytes = sjKsQrcodeService.generateQrcodeAndExportPdf(params.getQuantity());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "二维码_" + System.currentTimeMillis() + ".pdf");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

}
