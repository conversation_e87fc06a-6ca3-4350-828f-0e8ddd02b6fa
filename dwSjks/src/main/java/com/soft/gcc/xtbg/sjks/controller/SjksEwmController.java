package com.soft.gcc.xtbg.sjks.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.params.GenerateQrcodeParams;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@RequestMapping("/sjks/ewm")
@RestController
public class SjksEwmController extends BaseController {

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeService sjKsQrcodeService;

    @PostMapping("/getList")
    public Result<Object> getList(@RequestBody SjKsQrcode sjKsQrcode) {
        return Result.ok(sjKsQrcodeDetailService.getList(sjKsQrcode));
    }

    /**
     * 生成二维码并导出PDF
     */
    @PostMapping("/generate")
    public Result<Object> generateQrcode(@Valid @RequestBody GenerateQrcodeParams params, HttpServletResponse response) {
        try {
            PersonEntity persen = user();
            if (persen == null) {
                throw new RuntimeException("用户未登录");
            }

            byte[] pdfBytes = sjKsQrcodeService.generateQrcodeAndExportPdf(params.getQuantity(), persen);

            // 设置响应头
            String fileName = "二维码_" + System.currentTimeMillis() + ".pdf";
            response.setContentType("application/pdf");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 写入响应流
            response.getOutputStream().write(pdfBytes);
            response.getOutputStream().flush();

            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("生成失败：" + e.getMessage());
        }
    }

}
