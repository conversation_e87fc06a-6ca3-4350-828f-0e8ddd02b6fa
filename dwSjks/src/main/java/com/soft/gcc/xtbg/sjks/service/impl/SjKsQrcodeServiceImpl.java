package com.soft.gcc.xtbg.sjks.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail;
import com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeMapper;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.soft.gcc.xtbg.sjks.util.QRCodeGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

// PDF相关导入
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.google.zxing.WriterException;

import javax.imageio.ImageIO;

/**
* <AUTHOR>
* @description 针对表【sj_ks_qrcode(设计-考试-二维码主表)】的数据库操作Service实现
* @createDate 2025-07-30 14:15:09
*/
@Service
@Slave
public class SjKsQrcodeServiceImpl extends ServiceImpl<SjKsQrcodeMapper, SjKsQrcode> implements SjKsQrcodeService{

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public byte[] generateQrcodeAndExportPdf(Integer quantity) {
        Date now = new Date();
        int currentYear = now.getYear() + 1900; // Date.getYear() returns year - 1900
        int currentMonth = now.getMonth() + 1;   // Date.getMonth() returns 0-11

        // 查询当前年月的最大批次
        Integer maxBatch = getMaxBatchByYearMonth(currentYear, currentMonth);
        int newBatch = maxBatch == null ? 1 : maxBatch + 1;

        // 插入主表记录
        SjKsQrcode qrcode = new SjKsQrcode();
        qrcode.setYear(currentYear);
        qrcode.setMonth(currentMonth);
        qrcode.setBatch(newBatch);
        qrcode.setQrCodeNumber(quantity);
        qrcode.setCreateTime(now);
        qrcode.setDeleted("0");
        save(qrcode);

        // 生成详情记录
        List<SjKsQrcodeDetail> detailList = generateQrcodeDetails(qrcode.getId(), currentYear, currentMonth, newBatch, quantity);
        sjKsQrcodeDetailService.saveBatch(detailList);

        // 直接生成并返回PDF
        try {
            return generatePdfWithQrcodes(detailList);
        } catch (Exception e) {
            throw new RuntimeException("生成PDF失败：" + e.getMessage(), e);
        }
    }

    /**
     * 查询指定年月的最大批次
     */
    private Integer getMaxBatchByYearMonth(int year, int month) {
        LambdaQueryWrapper<SjKsQrcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcode::getYear, year)
               .eq(SjKsQrcode::getMonth, month)
               .eq(SjKsQrcode::getDeleted, "0")
               .orderByDesc(SjKsQrcode::getBatch)
               .last("LIMIT 1");

        SjKsQrcode maxRecord = getOne(wrapper);
        return maxRecord != null ? maxRecord.getBatch() : null;
    }

    /**
     * 生成二维码详情记录
     */
    private List<SjKsQrcodeDetail> generateQrcodeDetails(Integer qrCodeId, int year, int month, int batch, int quantity) {
        List<SjKsQrcodeDetail> detailList = new ArrayList<>();
        Set<String> generatedCodes = new HashSet<>();
        Date now = new Date();

        // 格式化年份和月份
        String yearStr = String.format("%02d", year % 100); // 取年份后两位
        String monthStr = String.format("%02d", month);
        String batchStr = String.format("%02d", batch);

        for (int i = 0; i < quantity; i++) {
            String qrCode;
            do {
                // 生成6位随机数字+大写字母
                String randomStr = generateRandomString(6);
                qrCode = yearStr + monthStr + randomStr + batchStr;
            } while (generatedCodes.contains(qrCode) || isQrcodeExists(qrCode));

            generatedCodes.add(qrCode);

            SjKsQrcodeDetail detail = new SjKsQrcodeDetail();
            detail.setQrCodeId(qrCodeId);
            detail.setQrCode(qrCode);
            detail.setCreateTime(now);
            detail.setDeleted("0");
            detailList.add(detail);
        }

        return detailList;
    }

    /**
     * 生成随机字符串（数字+大写字母）
     */
    private String generateRandomString(int length) {
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }

    /**
     * 检查二维码是否已存在
     */
    private boolean isQrcodeExists(String qrCode) {
        LambdaQueryWrapper<SjKsQrcodeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcodeDetail::getQrCode, qrCode)
               .eq(SjKsQrcodeDetail::getDeleted, "0");

        return sjKsQrcodeDetailService.count(wrapper) > 0;
    }



    /**
     * 生成包含二维码的PDF
     */
    private byte[] generatePdfWithQrcodes(List<SjKsQrcodeDetail> detailList) throws DocumentException, IOException, WriterException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, baos);

        document.open();

        // 每行1个二维码，每页最多5行（25个二维码）
        int qrCodesPerRow = 1;
        int rowsPerPage = 5;
        int qrCodesPerPage = qrCodesPerRow * rowsPerPage;

        // 二维码尺寸
        int qrCodeSize = 100;
        float qrCodeWidth = 100f;
        float qrCodeHeight = 100f;

        // 页面边距和间距
        float pageWidth = PageSize.A4.getWidth();
        float pageHeight = PageSize.A4.getHeight();
        float margin = 50f;
        float availableWidth = pageWidth - 2 * margin;
        float availableHeight = pageHeight - 2 * margin;

        // 计算间距
        float horizontalSpacing = (availableWidth - qrCodesPerRow * qrCodeWidth) / (qrCodesPerRow - 1);
        float verticalSpacing = (availableHeight - rowsPerPage * qrCodeHeight) / (rowsPerPage - 1);

        int currentPage = 0;
        int qrCodeIndex = 0;

        for (SjKsQrcodeDetail detail : detailList) {
            // 检查是否需要新页面
            if (qrCodeIndex % qrCodesPerPage == 0 && qrCodeIndex > 0) {
                document.newPage();
                currentPage++;
            }

            // 生成二维码图片
            BufferedImage qrImage = QRCodeGenerator.generateQRCodeImage(detail.getQrCode(), qrCodeSize, qrCodeSize);

            // 转换为iText Image
            ByteArrayOutputStream imageStream = new ByteArrayOutputStream();
            ImageIO.write(qrImage, "PNG", imageStream);
            Image pdfImage = Image.getInstance(imageStream.toByteArray());

            // 计算当前二维码在页面中的位置
            int positionInPage = qrCodeIndex % qrCodesPerPage;
            int row = positionInPage / qrCodesPerRow;
            int col = positionInPage % qrCodesPerRow;

            // 计算坐标（从页面底部开始计算）
            float x = margin + col * (qrCodeWidth + horizontalSpacing);
            float y = pageHeight - margin - (row + 1) * qrCodeHeight - row * verticalSpacing;

            // 设置图片位置和大小
            pdfImage.setAbsolutePosition(x, y);
            pdfImage.scaleAbsolute(qrCodeWidth, qrCodeHeight);

            // 添加到文档
            document.add(pdfImage);

            qrCodeIndex++;
        }

        document.close();
        return baos.toByteArray();
    }
}




